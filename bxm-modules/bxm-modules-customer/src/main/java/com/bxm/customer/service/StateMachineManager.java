package com.bxm.customer.service;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 状态机管理器
 *
 * 统一管理增值交付单的状态转换逻辑
 * 负责协调各个状态变更策略，执行状态转换操作
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Service
public class StateMachineManager {

    @Autowired
    private List<StatusChangeStrategy> changeStrategies;

    /**
     * 初始化后变更策略注入情况
     */
    @PostConstruct
    public void init() {
        log.info("StateMachineManager initialized with {} change strategies:", changeStrategies.size());
        for (StatusChangeStrategy strategy : changeStrategies) {
            log.info("- {}: supports status {}",
                    strategy.getStrategyName(),
                    strategy.getSupportedCurrentStatus().getDescription());
        }

        if (changeStrategies.isEmpty()) {
            log.warn("No change strategies found! Please check @Component annotations on strategy classes.");
        }
    }

    /**
     * 验证并执行状态变更（不包含数据库操作）
     *
     * @param order 交付单实体
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @param request 状态变更请求
     * @throws IllegalArgumentException 当验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    public void validateAndChangeStatus(ValueAddedDeliveryOrder order,
                                      ValueAddedDeliveryOrderStatus currentStatus,
                                      ValueAddedDeliveryOrderStatus targetStatus,
                                      StatusChangeRequestDTO request) {
        log.info("Starting status validation for order: {} from {} to {}",
                order.getDeliveryOrderNo(), currentStatus.getDescription(), targetStatus.getDescription());

        // 1. 查找并执行变更策略
        StatusChangeStrategy strategy = findChangeStrategy(currentStatus, targetStatus);
        if (strategy == null) {
            throw new IllegalArgumentException(
                String.format("不支持从状态 %s 转换到状态 %s",
                    currentStatus.getDescription(), targetStatus.getDescription()));
        }

        // 2. 执行验证
        try {
            strategy.validate(order, request);
            log.info("Status change validation passed for order: {} using strategy: {}",
                    order.getDeliveryOrderNo(), strategy.getStrategyName());
        } catch (Exception e) {
            log.error("Status change validation failed for order: {} using strategy: {}, error: {}",
                    order.getDeliveryOrderNo(), strategy.getStrategyName(), e.getMessage());
            throw e;
        }

        // 3. 更新状态（在调用方的Service中会保存到数据库）
        order.setStatus(targetStatus.getCode());

        log.info("Status validation completed for order: {} from {} to {}",
                order.getDeliveryOrderNo(), currentStatus.getDescription(), targetStatus.getDescription());
    }

    /**
     * 获取指定状态的可用下一状态列表
     * 
     * @param currentStatus 当前状态
     * @return 可用的下一状态列表
     */
    public List<ValueAddedDeliveryOrderStatus> getAvailableNextStatuses(ValueAddedDeliveryOrderStatus currentStatus) {
        if (currentStatus == null) {
            return new ArrayList<>();
        }

        List<ValueAddedDeliveryOrderStatus> availableStatuses = new ArrayList<>();
        
        // 遍历所有可能的目标状态
        for (ValueAddedDeliveryOrderStatus targetStatus : ValueAddedDeliveryOrderStatus.values()) {
            if (targetStatus == currentStatus) {
                continue; // 跳过相同状态
            }
            
            // 检查是否有策略支持此转换
            StatusChangeStrategy strategy = findChangeStrategy(currentStatus, targetStatus);
            if (strategy != null) {
                availableStatuses.add(targetStatus);
            }
        }

        return availableStatuses;
    }

    /**
     * 查找支持指定状态转换的变更策略
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 支持的变更策略，如果没有则返回null
     */
    private StatusChangeStrategy findChangeStrategy(ValueAddedDeliveryOrderStatus currentStatus,
                                                          ValueAddedDeliveryOrderStatus targetStatus) {
        return changeStrategies.stream()
                .filter(strategy -> strategy.supports(currentStatus, targetStatus))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有状态的转换规则映射
     * 
     * @return 状态转换规则映射 (当前状态 -> 可转换状态列表)
     */
    public Map<ValueAddedDeliveryOrderStatus, List<ValueAddedDeliveryOrderStatus>> getAllTransitionRules() {
        return Arrays.stream(ValueAddedDeliveryOrderStatus.values())
                .collect(Collectors.toMap(
                    status -> status,
                    this::getAvailableNextStatuses
                ));
    }

    /**
     * 获取策略覆盖情况（用于调试）
     *
     * @return 策略覆盖情况的描述
     */
    public String getStrategyCoverage() {
        StringBuilder sb = new StringBuilder();
        sb.append("策略覆盖情况:\n");

        for (ValueAddedDeliveryOrderStatus status : ValueAddedDeliveryOrderStatus.values()) {
            List<ValueAddedDeliveryOrderStatus> availableTargets = getAvailableNextStatuses(status);
            sb.append(String.format("- %s (%s): 可转换到 %d 个状态\n",
                    status.getCode(),
                    status.getDescription(),
                    availableTargets.size()));

            for (ValueAddedDeliveryOrderStatus target : availableTargets) {
                StatusChangeStrategy strategy = findChangeStrategy(status, target);
                sb.append(String.format("  → %s (%s) [%s]\n",
                        target.getCode(),
                        target.getDescription(),
                        strategy != null ? strategy.getStrategyName() : "无策略"));
            }
        }

        return sb.toString();
    }
}
